package eu.torvian.chatbot.server.service.core.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ChatSessionSummary
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.SessionError
import eu.torvian.chatbot.server.service.core.error.session.*
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import io.mockk.*
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.*

/**
 * Unit tests for [SessionServiceImpl].
 *
 * This test suite verifies that [SessionServiceImpl] correctly orchestrates
 * calls to the underlying DAO and handles business logic validation.
 * All dependencies ([SessionDao], [TransactionScope]) are mocked using MockK.
 */
class SessionServiceImplTest {

    // Mocked dependencies
    private lateinit var sessionDao: SessionDao
    private lateinit var transactionScope: TransactionScope

    // Class under test
    private lateinit var sessionService: SessionServiceImpl

    // Test data
    private val testSessionSummary1 = ChatSessionSummary(
        id = 1L,
        name = "Test Session 1",
        groupId = 1L,
        groupName = "Test Group",
        createdAt = Instant.fromEpochMilliseconds(1234567890000L),
        updatedAt = Instant.fromEpochMilliseconds(1234567890000L)
    )

    private val testSessionSummary2 = ChatSessionSummary(
        id = 2L,
        name = "Test Session 2",
        groupId = null,
        groupName = null,
        createdAt = Instant.fromEpochMilliseconds(1234567890000L),
        updatedAt = Instant.fromEpochMilliseconds(1234567890000L)
    )

    private val testSession = ChatSession(
        id = 1L,
        name = "Test Session",
        groupId = 1L,
        currentModelId = 1L,
        currentSettingsId = 1L,
        leafMessageId = null,
        createdAt = Instant.fromEpochMilliseconds(1234567890000L),
        updatedAt = Instant.fromEpochMilliseconds(1234567890000L),
        messages = emptyList()
    )

    @BeforeEach
    fun setUp() {
        // Create mocks for all dependencies
        sessionDao = mockk()
        transactionScope = mockk()

        // Create the service instance with mocked dependencies
        sessionService = SessionServiceImpl(sessionDao, transactionScope)

        // Mock the transaction scope to execute blocks directly
        coEvery { transactionScope.transaction(any<suspend () -> Any>()) } coAnswers {
            val block = firstArg<suspend () -> Any>()
            block()
        }
    }

    @AfterEach
    fun tearDown() {
        // Clear all mocks after each test to ensure isolation
        clearMocks(sessionDao, transactionScope)
    }

    // --- getAllSessionsSummaries Tests ---

    @Test
    fun `getAllSessionsSummaries should return list of session summaries from DAO`() = runTest {
        // Arrange
        val expectedSummaries = listOf(testSessionSummary1, testSessionSummary2)
        coEvery { sessionDao.getAllSessions() } returns expectedSummaries

        // Act
        val result = sessionService.getAllSessionsSummaries()

        // Assert
        assertEquals(expectedSummaries, result, "Should return the session summaries from DAO")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.getAllSessions() }
    }

    @Test
    fun `getAllSessionsSummaries should return empty list when no sessions exist`() = runTest {
        // Arrange
        coEvery { sessionDao.getAllSessions() } returns emptyList()

        // Act
        val result = sessionService.getAllSessionsSummaries()

        // Assert
        assertTrue(result.isEmpty(), "Should return empty list when no sessions exist")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.getAllSessions() }
    }

    // --- createSession Tests ---

    @Test
    fun `createSession should create session successfully with valid name`() = runTest {
        // Arrange
        val sessionName = "New Session"
        coEvery { sessionDao.insertSession(sessionName) } returns testSession.right()

        // Act
        val result = sessionService.createSession(sessionName)

        // Assert
        assertTrue(result.isRight(), "Should return Right for successful creation")
        assertEquals(testSession, result.getOrNull(), "Should return the created session")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.insertSession(sessionName) }
    }

    @Test
    fun `createSession should create session successfully with null name using default`() = runTest {
        // Arrange
        val defaultName = "New Chat"
        coEvery { sessionDao.insertSession(defaultName) } returns testSession.right()

        // Act
        val result = sessionService.createSession(null)

        // Assert
        assertTrue(result.isRight(), "Should return Right for successful creation")
        assertEquals(testSession, result.getOrNull(), "Should return the created session")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.insertSession(defaultName) }
    }

    @Test
    fun `createSession should return InvalidName error for blank name`() = runTest {
        // Arrange
        val blankName = "   "

        // Act
        val result = sessionService.createSession(blankName)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for blank name")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is CreateSessionError.InvalidName, "Should be InvalidName error")
        assertEquals("Session name cannot be blank.", (error as CreateSessionError.InvalidName).reason)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 0) { sessionDao.insertSession(any()) }
    }

    @Test
    fun `createSession should return InvalidRelatedEntity error for foreign key violation`() = runTest {
        // Arrange
        val sessionName = "New Session"
        val daoError = SessionError.ForeignKeyViolation("Invalid group ID")
        coEvery { sessionDao.insertSession(sessionName) } returns daoError.left()

        // Act
        val result = sessionService.createSession(sessionName)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for foreign key violation")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is CreateSessionError.InvalidRelatedEntity, "Should be InvalidRelatedEntity error")
        assertEquals("Invalid group ID", (error as CreateSessionError.InvalidRelatedEntity).message)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.insertSession(sessionName) }
    }

    // --- getSessionDetails Tests ---

    @Test
    fun `getSessionDetails should return session when it exists`() = runTest {
        // Arrange
        val sessionId = 1L
        coEvery { sessionDao.getSessionById(sessionId) } returns testSession.right()

        // Act
        val result = sessionService.getSessionDetails(sessionId)

        // Assert
        assertTrue(result.isRight(), "Should return Right for existing session")
        assertEquals(testSession, result.getOrNull(), "Should return the correct session")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.getSessionById(sessionId) }
    }

    @Test
    fun `getSessionDetails should return SessionNotFound error when session does not exist`() = runTest {
        // Arrange
        val sessionId = 999L
        val daoError = SessionError.SessionNotFound(sessionId)
        coEvery { sessionDao.getSessionById(sessionId) } returns daoError.left()

        // Act
        val result = sessionService.getSessionDetails(sessionId)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for non-existent session")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is GetSessionDetailsError.SessionNotFound, "Should be SessionNotFound error")
        assertEquals(sessionId, (error as GetSessionDetailsError.SessionNotFound).id)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.getSessionById(sessionId) }
    }

    // --- updateSessionName Tests ---

    @Test
    fun `updateSessionName should update session name successfully`() = runTest {
        // Arrange
        val sessionId = 1L
        val newName = "Updated Session Name"
        coEvery { sessionDao.updateSessionName(sessionId, newName) } returns Unit.right()

        // Act
        val result = sessionService.updateSessionName(sessionId, newName)

        // Assert
        assertTrue(result.isRight(), "Should return Right for successful update")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.updateSessionName(sessionId, newName) }
    }

    @Test
    fun `updateSessionName should return InvalidName error for blank name`() = runTest {
        // Arrange
        val sessionId = 1L
        val blankName = "  "

        // Act
        val result = sessionService.updateSessionName(sessionId, blankName)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for blank name")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is UpdateSessionNameError.InvalidName, "Should be InvalidName error")
        assertEquals("Session name cannot be blank.", (error as UpdateSessionNameError.InvalidName).reason)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 0) { sessionDao.updateSessionName(any(), any()) }
    }

    @Test
    fun `updateSessionName should return SessionNotFound error when session does not exist`() = runTest {
        // Arrange
        val sessionId = 999L
        val newName = "Updated Name"
        val daoError = SessionError.SessionNotFound(sessionId)
        coEvery { sessionDao.updateSessionName(sessionId, newName) } returns daoError.left()

        // Act
        val result = sessionService.updateSessionName(sessionId, newName)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for non-existent session")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is UpdateSessionNameError.SessionNotFound, "Should be SessionNotFound error")
        assertEquals(sessionId, (error as UpdateSessionNameError.SessionNotFound).id)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.updateSessionName(sessionId, newName) }
    }
}
