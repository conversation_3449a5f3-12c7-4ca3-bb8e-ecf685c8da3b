package eu.torvian.chatbot.server.service.core.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.LLMProvider
import eu.torvian.chatbot.common.models.LLMProviderType
import eu.torvian.chatbot.server.data.dao.LLMProviderDao
import eu.torvian.chatbot.server.data.dao.ModelDao
import eu.torvian.chatbot.server.data.dao.error.LLMProviderError
import eu.torvian.chatbot.server.service.core.error.provider.*
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.*

/**
 * Unit tests for [LLMProviderServiceImpl].
 *
 * This test suite verifies that [LLMProviderServiceImpl] correctly orchestrates
 * calls to the underlying DAOs and handles business logic validation.
 * All dependencies ([LLMProviderDao], [ModelDao], [CredentialManager], [TransactionScope]) are mocked using MockK.
 */
class LLMProviderServiceImplTest {

    // Mocked dependencies
    private lateinit var llmProviderDao: LLMProviderDao
    private lateinit var modelDao: ModelDao
    private lateinit var credentialManager: CredentialManager
    private lateinit var transactionScope: TransactionScope

    // Class under test
    private lateinit var llmProviderService: LLMProviderServiceImpl

    // Test data
    private val testProvider1 = LLMProvider(
        id = 1L,
        apiKeyId = "test-key-id-1",
        name = "OpenAI",
        description = "OpenAI Provider",
        baseUrl = "https://api.openai.com/v1",
        type = LLMProviderType.OPENAI
    )

    private val testProvider2 = LLMProvider(
        id = 2L,
        apiKeyId = null,
        name = "Ollama",
        description = "Local Ollama Provider",
        baseUrl = "http://localhost:11434",
        type = LLMProviderType.OLLAMA
    )

    private val testModel1 = LLMModel(
        id = 1L,
        name = "gpt-3.5-turbo",
        providerId = 1L,
        active = true,
        displayName = "GPT-3.5 Turbo"
    )

    @BeforeEach
    fun setUp() {
        // Create mocks for all dependencies
        llmProviderDao = mockk()
        modelDao = mockk()
        credentialManager = mockk()
        transactionScope = mockk()

        // Create the service instance with mocked dependencies
        llmProviderService = LLMProviderServiceImpl(llmProviderDao, modelDao, credentialManager, transactionScope)

        // Mock the transaction scope to execute blocks directly
        coEvery { transactionScope.transaction(any<suspend () -> Any>()) } coAnswers {
            val block = firstArg<suspend () -> Any>()
            block()
        }
    }

    @AfterEach
    fun tearDown() {
        // Clear all mocks after each test to ensure isolation
        clearMocks(llmProviderDao, modelDao, credentialManager, transactionScope)
    }

    // --- getAllProviders Tests ---

    @Test
    fun `getAllProviders should return list of providers from DAO`() = runTest {
        // Arrange
        val expectedProviders = listOf(testProvider1, testProvider2)
        coEvery { llmProviderDao.getAllProviders() } returns expectedProviders

        // Act
        val result = llmProviderService.getAllProviders()

        // Assert
        assertEquals(expectedProviders, result, "Should return the providers from DAO")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { llmProviderDao.getAllProviders() }
    }

    @Test
    fun `getAllProviders should return empty list when no providers exist`() = runTest {
        // Arrange
        coEvery { llmProviderDao.getAllProviders() } returns emptyList()

        // Act
        val result = llmProviderService.getAllProviders()

        // Assert
        assertTrue(result.isEmpty(), "Should return empty list when no providers exist")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { llmProviderDao.getAllProviders() }
    }

    // --- getProviderById Tests ---

    @Test
    fun `getProviderById should return provider when it exists`() = runTest {
        // Arrange
        val providerId = 1L
        coEvery { llmProviderDao.getProviderById(providerId) } returns testProvider1.right()

        // Act
        val result = llmProviderService.getProviderById(providerId)

        // Assert
        assertTrue(result.isRight(), "Should return Right for existing provider")
        assertEquals(testProvider1, result.getOrNull(), "Should return the correct provider")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { llmProviderDao.getProviderById(providerId) }
    }

    @Test
    fun `getProviderById should return ProviderNotFound error when provider does not exist`() = runTest {
        // Arrange
        val providerId = 999L
        val daoError = LLMProviderError.LLMProviderNotFound(providerId)
        coEvery { llmProviderDao.getProviderById(providerId) } returns daoError.left()

        // Act
        val result = llmProviderService.getProviderById(providerId)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for non-existent provider")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is GetProviderError.ProviderNotFound, "Should be ProviderNotFound error")
        assertEquals(providerId, (error as GetProviderError.ProviderNotFound).id)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { llmProviderDao.getProviderById(providerId) }
    }

    // --- addProvider Tests ---

    @Test
    fun `addProvider should create provider successfully with credential`() = runTest {
        // Arrange
        val name = "OpenAI"
        val description = "OpenAI Provider"
        val baseUrl = "https://api.openai.com/v1"
        val type = LLMProviderType.OPENAI
        val credential = "test-api-key"
        val credentialAlias = "test-key-id"
        
        every { credentialManager.storeCredential(credential) } returns credentialAlias
        coEvery { llmProviderDao.insertProvider(credentialAlias, name, description, baseUrl, type) } returns testProvider1.right()

        // Act
        val result = llmProviderService.addProvider(name, description, baseUrl, type, credential)

        // Assert
        assertTrue(result.isRight(), "Should return Right for successful creation")
        assertEquals(testProvider1, result.getOrNull(), "Should return the created provider")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        verify(exactly = 1) { credentialManager.storeCredential(credential) }
        coVerify(exactly = 1) { llmProviderDao.insertProvider(credentialAlias, name, description, baseUrl, type) }
    }

    @Test
    fun `addProvider should create provider successfully without credential`() = runTest {
        // Arrange
        val name = "Ollama"
        val description = "Local Ollama Provider"
        val baseUrl = "http://localhost:11434"
        val type = LLMProviderType.OLLAMA
        
        coEvery { llmProviderDao.insertProvider(null, name, description, baseUrl, type) } returns testProvider2.right()

        // Act
        val result = llmProviderService.addProvider(name, description, baseUrl, type, null)

        // Assert
        assertTrue(result.isRight(), "Should return Right for successful creation")
        assertEquals(testProvider2, result.getOrNull(), "Should return the created provider")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        verify(exactly = 0) { credentialManager.storeCredential(any()) }
        coVerify(exactly = 1) { llmProviderDao.insertProvider(null, name, description, baseUrl, type) }
    }

    @Test
    fun `addProvider should return InvalidInput error for blank name`() = runTest {
        // Arrange
        val blankName = "   "
        val description = "Test Description"
        val baseUrl = "https://api.test.com"
        val type = LLMProviderType.OPENAI

        // Act
        val result = llmProviderService.addProvider(blankName, description, baseUrl, type, null)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for blank name")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is AddProviderError.InvalidInput, "Should be InvalidInput error")
        assertEquals("Provider name cannot be blank.", (error as AddProviderError.InvalidInput).reason)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        verify(exactly = 0) { credentialManager.storeCredential(any()) }
        coVerify(exactly = 0) { llmProviderDao.insertProvider(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `addProvider should return InvalidInput error for blank baseUrl`() = runTest {
        // Arrange
        val name = "Test Provider"
        val description = "Test Description"
        val blankBaseUrl = "   "
        val type = LLMProviderType.OPENAI

        // Act
        val result = llmProviderService.addProvider(name, description, blankBaseUrl, type, null)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for blank baseUrl")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is AddProviderError.InvalidInput, "Should be InvalidInput error")
        assertEquals("Provider base URL cannot be blank.", (error as AddProviderError.InvalidInput).reason)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        verify(exactly = 0) { credentialManager.storeCredential(any()) }
        coVerify(exactly = 0) { llmProviderDao.insertProvider(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `addProvider should return InvalidInput error for blank credential when provided`() = runTest {
        // Arrange
        val name = "Test Provider"
        val description = "Test Description"
        val baseUrl = "https://api.test.com"
        val type = LLMProviderType.OPENAI
        val blankCredential = "   "

        // Act
        val result = llmProviderService.addProvider(name, description, baseUrl, type, blankCredential)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for blank credential")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is AddProviderError.InvalidInput, "Should be InvalidInput error")
        assertEquals("Provider credential cannot be blank when provided.", (error as AddProviderError.InvalidInput).reason)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        verify(exactly = 0) { credentialManager.storeCredential(any()) }
        coVerify(exactly = 0) { llmProviderDao.insertProvider(any(), any(), any(), any(), any()) }
    }
}
