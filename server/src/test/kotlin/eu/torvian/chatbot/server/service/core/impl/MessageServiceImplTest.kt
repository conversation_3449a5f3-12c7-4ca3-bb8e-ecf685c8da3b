package eu.torvian.chatbot.server.service.core.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.data.dao.MessageDao
import eu.torvian.chatbot.server.data.dao.SessionDao
import eu.torvian.chatbot.server.data.dao.error.MessageError
import eu.torvian.chatbot.server.data.dao.error.SessionError
import eu.torvian.chatbot.server.service.core.LLMModelService
import eu.torvian.chatbot.server.service.core.LLMProviderService
import eu.torvian.chatbot.server.service.core.ModelSettingsService
import eu.torvian.chatbot.server.service.core.error.message.*
import eu.torvian.chatbot.server.service.core.error.model.GetModelError
import eu.torvian.chatbot.server.service.core.error.provider.GetProviderError
import eu.torvian.chatbot.server.service.core.error.settings.GetSettingsByIdError
import eu.torvian.chatbot.server.service.llm.LLMApiClient
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import io.mockk.*
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.*

/**
 * Unit tests for [MessageServiceImpl].
 *
 * This test suite verifies that [MessageServiceImpl] correctly orchestrates
 * calls to the underlying DAOs and services, and handles business logic validation.
 * All dependencies are mocked using MockK.
 */
class MessageServiceImplTest {

    // Mocked dependencies
    private lateinit var messageDao: MessageDao
    private lateinit var sessionDao: SessionDao
    private lateinit var llmModelService: LLMModelService
    private lateinit var modelSettingsService: ModelSettingsService
    private lateinit var llmProviderService: LLMProviderService
    private lateinit var llmApiClient: LLMApiClient
    private lateinit var credentialManager: CredentialManager
    private lateinit var transactionScope: TransactionScope

    // Class under test
    private lateinit var messageService: MessageServiceImpl

    // Test data
    private val testMessage1 = ChatMessage(
        id = 1L,
        sessionId = 1L,
        content = "Hello, how are you?",
        role = ChatRole.USER,
        parentMessageId = null,
        modelId = null,
        settingsId = null,
        createdAt = Instant.fromEpochMilliseconds(1234567890000L)
    )

    private val testMessage2 = ChatMessage(
        id = 2L,
        sessionId = 1L,
        content = "I'm doing well, thank you!",
        role = ChatRole.ASSISTANT,
        parentMessageId = 1L,
        modelId = 1L,
        settingsId = 1L,
        createdAt = Instant.fromEpochMilliseconds(1234567890000L)
    )

    private val testSession = ChatSession(
        id = 1L,
        name = "Test Session",
        groupId = null,
        currentModelId = 1L,
        currentSettingsId = 1L,
        leafMessageId = null,
        createdAt = Instant.fromEpochMilliseconds(1234567890000L),
        updatedAt = Instant.fromEpochMilliseconds(1234567890000L),
        messages = emptyList()
    )

    private val testModel = LLMModel(
        id = 1L,
        name = "gpt-3.5-turbo",
        providerId = 1L,
        active = true,
        displayName = "GPT-3.5 Turbo"
    )

    private val testProvider = LLMProvider(
        id = 1L,
        apiKeyId = "test-key-id",
        name = "OpenAI",
        description = "OpenAI Provider",
        baseUrl = "https://api.openai.com/v1",
        type = LLMProviderType.OPENAI
    )

    private val testSettings = ModelSettings(
        id = 1L,
        name = "Default",
        modelId = 1L,
        systemMessage = "You are a helpful assistant.",
        temperature = 0.7f,
        maxTokens = 1000,
        customParamsJson = null
    )

    @BeforeEach
    fun setUp() {
        // Create mocks for all dependencies
        messageDao = mockk()
        sessionDao = mockk()
        llmModelService = mockk()
        modelSettingsService = mockk()
        llmProviderService = mockk()
        llmApiClient = mockk()
        credentialManager = mockk()
        transactionScope = mockk()

        // Create the service instance with mocked dependencies
        messageService = MessageServiceImpl(
            messageDao, sessionDao, llmModelService, modelSettingsService,
            llmProviderService, llmApiClient, credentialManager, transactionScope
        )

        // Mock the transaction scope to execute blocks directly
        coEvery { transactionScope.transaction(any<suspend () -> Any>()) } coAnswers {
            val block = firstArg<suspend () -> Any>()
            block()
        }
    }

    @AfterEach
    fun tearDown() {
        // Clear all mocks after each test to ensure isolation
        clearMocks(
            messageDao, sessionDao, llmModelService, modelSettingsService,
            llmProviderService, llmApiClient, credentialManager, transactionScope
        )
    }

    // --- getMessagesBySessionId Tests ---

    @Test
    fun `getMessagesBySessionId should return list of messages from DAO`() = runTest {
        // Arrange
        val sessionId = 1L
        val expectedMessages = listOf(testMessage1, testMessage2)
        coEvery { messageDao.getMessagesBySessionId(sessionId) } returns expectedMessages

        // Act
        val result = messageService.getMessagesBySessionId(sessionId)

        // Assert
        assertEquals(expectedMessages, result, "Should return the messages from DAO")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { messageDao.getMessagesBySessionId(sessionId) }
    }

    @Test
    fun `getMessagesBySessionId should return empty list when no messages exist`() = runTest {
        // Arrange
        val sessionId = 1L
        coEvery { messageDao.getMessagesBySessionId(sessionId) } returns emptyList()

        // Act
        val result = messageService.getMessagesBySessionId(sessionId)

        // Assert
        assertTrue(result.isEmpty(), "Should return empty list when no messages exist")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { messageDao.getMessagesBySessionId(sessionId) }
    }

    // --- updateMessageContent Tests ---

    @Test
    fun `updateMessageContent should update message content successfully`() = runTest {
        // Arrange
        val messageId = 1L
        val newContent = "Updated content"
        val updatedMessage = testMessage1.copy(content = newContent)
        coEvery { messageDao.updateMessageContent(messageId, newContent) } returns updatedMessage.right()

        // Act
        val result = messageService.updateMessageContent(messageId, newContent)

        // Assert
        assertTrue(result.isRight(), "Should return Right for successful update")
        assertEquals(updatedMessage, result.getOrNull(), "Should return the updated message")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { messageDao.updateMessageContent(messageId, newContent) }
    }

    @Test
    fun `updateMessageContent should return MessageNotFound error when message does not exist`() = runTest {
        // Arrange
        val messageId = 999L
        val newContent = "Updated content"
        val daoError = MessageError.MessageNotFound(messageId)
        coEvery { messageDao.updateMessageContent(messageId, newContent) } returns daoError.left()

        // Act
        val result = messageService.updateMessageContent(messageId, newContent)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for non-existent message")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is UpdateMessageContentError.MessageNotFound, "Should be MessageNotFound error")
        assertEquals(messageId, (error as UpdateMessageContentError.MessageNotFound).id)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { messageDao.updateMessageContent(messageId, newContent) }
    }

    // --- deleteMessage Tests ---

    @Test
    fun `deleteMessage should delete message successfully`() = runTest {
        // Arrange
        val messageId = 1L
        coEvery { messageDao.deleteMessage(messageId) } returns Unit.right()

        // Act
        val result = messageService.deleteMessage(messageId)

        // Assert
        assertTrue(result.isRight(), "Should return Right for successful deletion")
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { messageDao.deleteMessage(messageId) }
    }

    @Test
    fun `deleteMessage should return MessageNotFound error when message does not exist`() = runTest {
        // Arrange
        val messageId = 999L
        val daoError = MessageError.MessageNotFound(messageId)
        coEvery { messageDao.deleteMessage(messageId) } returns daoError.left()

        // Act
        val result = messageService.deleteMessage(messageId)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for non-existent message")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is DeleteMessageError.MessageNotFound, "Should be MessageNotFound error")
        assertEquals(messageId, (error as DeleteMessageError.MessageNotFound).id)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { messageDao.deleteMessage(messageId) }
    }

    // --- processNewMessage Tests (Basic Cases) ---

    @Test
    fun `processNewMessage should return SessionNotFound error when session does not exist`() = runTest {
        // Arrange
        val sessionId = 999L
        val content = "Hello"
        val daoError = SessionError.SessionNotFound(sessionId)
        coEvery { sessionDao.getSessionById(sessionId) } returns daoError.left()

        // Act
        val result = messageService.processNewMessage(sessionId, content, null)

        // Assert
        assertTrue(result.isLeft(), "Should return Left for non-existent session")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is ProcessNewMessageError.SessionNotFound, "Should be SessionNotFound error")
        assertEquals(sessionId, (error as ProcessNewMessageError.SessionNotFound).sessionId)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.getSessionById(sessionId) }
    }

    @Test
    fun `processNewMessage should return ModelConfigurationError when no model is selected`() = runTest {
        // Arrange
        val sessionId = 1L
        val content = "Hello"
        val sessionWithoutModel = testSession.copy(currentModelId = null)
        coEvery { sessionDao.getSessionById(sessionId) } returns sessionWithoutModel.right()

        // Act
        val result = messageService.processNewMessage(sessionId, content, null)

        // Assert
        assertTrue(result.isLeft(), "Should return Left when no model is selected")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is ProcessNewMessageError.ModelConfigurationError, "Should be ModelConfigurationError")
        assertEquals("No model selected for session", (error as ProcessNewMessageError.ModelConfigurationError).reason)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.getSessionById(sessionId) }
    }

    @Test
    fun `processNewMessage should return ModelConfigurationError when no settings are selected`() = runTest {
        // Arrange
        val sessionId = 1L
        val content = "Hello"
        val sessionWithoutSettings = testSession.copy(currentSettingsId = null)
        coEvery { sessionDao.getSessionById(sessionId) } returns sessionWithoutSettings.right()

        // Act
        val result = messageService.processNewMessage(sessionId, content, null)

        // Assert
        assertTrue(result.isLeft(), "Should return Left when no settings are selected")
        val error = result.leftOrNull()
        assertNotNull(error, "Error should not be null")
        assertTrue(error is ProcessNewMessageError.ModelConfigurationError, "Should be ModelConfigurationError")
        assertEquals("No settings selected for session", (error as ProcessNewMessageError.ModelConfigurationError).reason)
        coVerify(exactly = 1) { transactionScope.transaction(any<suspend () -> Any>()) }
        coVerify(exactly = 1) { sessionDao.getSessionById(sessionId) }
    }
}
